"""
Test script to verify that table_summary flows correctly to all subagents.
"""

from langchain_core.messages import HumanMessage
from bond_ai.state import BondAIWorkflowState
from bond_ai.graph import create_workflow_from_registry

def test_table_summary_flow():
    """Test that table_summary is accessible to all subagents."""
    
    # Create the workflow
    workflow = create_workflow_from_registry()
    
    # Create initial state with a mock table_summary
    initial_state = {
        "messages": [HumanMessage(content="Test message to verify table_summary flow")],
        "table_summary": """
        Test Table Summary:
        - Column 1: Full Name (text data)
        - Column 2: Company (text data) 
        - Column 3: LinkedIn URL (URL data)
        - Column 4: Job Title (text data)
        """,
        "mode": "chat",
        "selected_row_ids": None,
        "selected_column_ids": None,
        "intents": None,
        "tool_calls": None,
        "plan_tasks": [],
        "active_task_id": None,
        "next": None,
        "last_error_message": None
    }
    
    # Test configuration
    config = {
        "configurable": {
            "table_id": "test_table_123",
            "model": "openai:gpt-4o-mini"
        }
    }
    
    print("🧪 Testing table_summary flow to subagents...")
    print(f"📊 Mock table_summary: {initial_state['table_summary']}")
    
    try:
        # This should run the table_indexing_node first, then supervisor
        result = workflow.invoke(initial_state, config)
        
        print("✅ Workflow executed successfully!")
        print(f"📋 Final state keys: {list(result.keys())}")
        
        if "table_summary" in result:
            print(f"📊 Final table_summary: {result['table_summary'][:200]}...")
        else:
            print("⚠️  No table_summary in final result")
            
        return result
        
    except Exception as e:
        print(f"❌ Error during workflow execution: {e}")
        return None

if __name__ == "__main__":
    test_table_summary_flow()
